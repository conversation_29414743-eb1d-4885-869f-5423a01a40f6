import Foundation

/// 聊天状态管理器 - 符合Repository模式的状态持久化服务
/// 遵循项目架构规范，避免直接使用UserDefaults
@MainActor
class EAChatStateManager: ObservableObject {
    
    // MARK: - 常量定义
    
    private enum Constants {
        static let stateValidityPeriod: TimeInterval = 300 // 5分钟状态有效期
        static let keyPrefix = "chat_state_"
        static let unsentMessagePrefix = "unsent_message_"
    }
    
    // MARK: - 状态数据结构
    
    private struct ChatState: Codable {
        let friendshipId: String
        let messagesCount: Int
        let lastMessageId: String
        let timestamp: TimeInterval
        let unsentMessage: String?
    }
    
    // MARK: - 私有属性
    
    private let storage: UserDefaults
    
    // MARK: - 初始化
    
    init(storage: UserDefaults = .standard) {
        self.storage = storage
    }
    
    // MARK: - 公共接口
    
    /// 保存聊天状态
    func saveChatState(
        friendshipId: UUID,
        messagesCount: Int,
        lastMessageId: String,
        unsentMessage: String?
    ) {
        let state = ChatState(
            friendshipId: friendshipId.uuidString,
            messagesCount: messagesCount,
            lastMessageId: lastMessageId,
            timestamp: Date().timeIntervalSince1970,
            unsentMessage: unsentMessage?.trimmingCharacters(in: .whitespacesAndNewlines)
        )
        
        let key = Constants.keyPrefix + friendshipId.uuidString
        
        do {
            let data = try JSONEncoder().encode(state)
            storage.set(data, forKey: key)
        } catch {
            // 🔑 编码失败时静默处理，不影响主要功能
        }
    }
    
    /// 恢复聊天状态
    func restoreChatState(for friendshipId: UUID) -> (messagesCount: Int, unsentMessage: String?)? {
        let key = Constants.keyPrefix + friendshipId.uuidString
        
        guard let data = storage.data(forKey: key),
              let state = try? JSONDecoder().decode(ChatState.self, from: data),
              state.friendshipId == friendshipId.uuidString else {
            return nil
        }
        
        // 检查状态是否过期
        let currentTimestamp = Date().timeIntervalSince1970
        let stateAge = currentTimestamp - state.timestamp
        
        guard stateAge < Constants.stateValidityPeriod else {
            // 状态已过期，清理并返回nil
            clearChatState(for: friendshipId)
            return nil
        }
        
        // 清理已使用的状态
        clearChatState(for: friendshipId)
        
        return (
            messagesCount: state.messagesCount,
            unsentMessage: state.unsentMessage
        )
    }
    
    /// 清理聊天状态
    func clearChatState(for friendshipId: UUID) {
        let key = Constants.keyPrefix + friendshipId.uuidString
        storage.removeObject(forKey: key)
    }
    
    /// 清理所有过期状态
    func cleanupExpiredStates() {
        let currentTimestamp = Date().timeIntervalSince1970
        let allKeys = storage.dictionaryRepresentation().keys
        
        for key in allKeys {
            guard key.hasPrefix(Constants.keyPrefix),
                  let data = storage.data(forKey: key),
                  let state = try? JSONDecoder().decode(ChatState.self, from: data) else {
                continue
            }
            
            let stateAge = currentTimestamp - state.timestamp
            if stateAge >= Constants.stateValidityPeriod {
                storage.removeObject(forKey: key)
            }
        }
    }
}

// MARK: - Environment支持

extension EnvironmentValues {
    @Entry var chatStateManager: EAChatStateManager = EAChatStateManager()
}
