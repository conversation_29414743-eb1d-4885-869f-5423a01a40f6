import Foundation
import SwiftData

/// 好友消息模型 - AI增强聊天系统
/// 记录好友之间的消息，支持多种消息类型和AI增强功能
@Model
final class EAFriendMessage: @unchecked Sendable {
    var id: UUID = UUID()
    var creationDate: Date = Date()
    
    // ✅ 新增：消息序列ID，解决排序问题
    var sequenceId: Int64 = 0               // 单调递增的序列ID，确保消息排序的绝对正确性
    
    /// 消息类型
    enum MessageType: String, Codable, CaseIterable {
        case text = "text"                  // 文本消息
        case image = "image"                // 图片消息
        case voice = "voice"                // 语音消息
        case video = "video"                // 视频消息
        case habitShare = "habit_share"     // 习惯分享
        case stellarEnergy = "stellar_energy" // 星际能量分享
        case aiSuggestion = "ai_suggestion" // AI建议消息
    }
    
    /// 消息状态
    enum MessageStatus: String, Codable, CaseIterable {
        case sent = "sent"          // 已发送
        case delivered = "delivered" // 已送达
        case read = "read"          // 已读
        case failed = "failed"      // 发送失败
    }
    
    var messageType: MessageType
    var content: String
    var status: MessageStatus
    var isRead: Bool = false
    var readDate: Date?
    var isDeleted: Bool = false
    var isRevoked: Bool = false         // 撤销标记
    var revokeDate: Date?               // 撤销时间
    
    // AI增强功能
    var aiSuggestionUsed: Bool = false      // 是否使用了AI建议
    var aiSentimentScore: Double?           // AI情感分析得分

    // 🔑 修复iOS 18.2+兼容性：使用字符串存储数组，避免序列化问题
    private var aiResponseSuggestionsString: String = "" // AI回复建议的字符串存储
    
    // 消息元数据
    var editedDate: Date?                   // 编辑时间
    
    // ✅ 优化：媒体文件存储 - 统一使用相对路径
    var mediaPath: String?                  // 媒体文件相对路径（由EAChatMediaManager管理）
    var thumbnailPath: String?              // 缩略图相对路径
    
    // 多媒体扩展属性
    var attachmentDuration: TimeInterval?   // 语音/视频时长（秒）
    var attachmentFileSize: Int64?          // 文件大小（字节）
    
    // ✅ 废弃：移除旧的URL字段，避免路径混乱
    // var attachmentURL: String?           // 已废弃，使用mediaPath替代
    // var thumbnailURL: String?            // 已废弃，使用thumbnailPath替代
    
    /// 🔑 第四阶段优化：发送者活动档案（普通属性，inverse在EAUserSocialActivity端定义）
    var senderActivity: EAUserSocialActivity?

    /// 🔑 第四阶段优化：接收者活动档案（普通属性，inverse在EAUserSocialActivity端定义）
    var receiverActivity: EAUserSocialActivity?

    /// 🔑 兼容性：发送者档案（计算属性，保持API兼容性）
    var senderProfile: EAUserSocialProfile? {
        return senderActivity?.socialProfile
    }

    /// 🔑 兼容性：接收者档案（计算属性，保持API兼容性）
    var receiverProfile: EAUserSocialProfile? {
        return receiverActivity?.socialProfile
    }

    /// 🔑 标准化：所属好友关系（普通属性，inverse在EAFriendship端已定义）
    var friendship: EAFriendship?
    
    // ✅ 新增：关系属性替代外键
    var replyToMessage: EAFriendMessage?        // 回复的消息（普通属性）
    
    init(content: String, messageType: MessageType = .text, sequenceId: Int64 = 0) {
        self.content = content
        self.messageType = messageType
        self.sequenceId = sequenceId
        self.status = .sent
        // ✅ 修复：不在init中赋值关系属性，遵循SwiftData最佳实践
        // 关系赋值将在Repository层的"插入→赋值→保存"流程中完成
    }
    
    /// 标记消息为已读
    func markAsRead() {
        isRead = true
        readDate = Date()
        status = .read
    }
    
    /// 标记消息为已送达
    func markAsDelivered() {
        if status == .sent {
            status = .delivered
        }
    }
    
    /// 标记消息发送失败
    func markAsFailed() {
        status = .failed
    }
    
    /// 软删除消息
    func softDelete() {
        isDeleted = true
    }
    
    /// 编辑消息内容
    func editContent(_ newContent: String) {
        content = newContent
        editedDate = Date()
    }
    
    // MARK: - 计算属性：数组访问接口（修复iOS 18.2+兼容性）

    /// AI回复建议数组的计算属性
    var aiResponseSuggestions: [String] {
        get {
            return aiResponseSuggestionsString.isEmpty ? [] : aiResponseSuggestionsString.components(separatedBy: "|||")
        }
        set {
            aiResponseSuggestionsString = newValue.joined(separator: "|||")
        }
    }

    /// 设置AI情感分析结果
    func setAISentimentAnalysis(score: Double, suggestions: [String]) {
        aiSentimentScore = score
        aiResponseSuggestions = suggestions
    }
    
    /// 标记使用了AI建议
    func markAISuggestionUsed() {
        aiSuggestionUsed = true
    }
    
    /// 检查消息是否可以编辑
    func canBeEdited() -> Bool {
        let timeLimit: TimeInterval = 15 * 60 // 15分钟内可编辑
        return !isDeleted && 
               !isRevoked &&
               messageType == .text && 
               Date().timeIntervalSince(creationDate) < timeLimit
    }
    
    /// 检查是否为多媒体消息
    func isMediaMessage() -> Bool {
        return [.image, .voice, .video].contains(messageType)
    }
    
    /// 获取格式化的文件大小
    func getFormattedFileSize() -> String? {
        guard let fileSize = attachmentFileSize else { return nil }
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useMB, .useKB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: fileSize)
    }
    
    /// 获取格式化的时长
    func getFormattedDuration() -> String? {
        guard let duration = attachmentDuration else { return nil }
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
    
    /// 检查消息是否可以撤销
    func canBeRevoked() -> Bool {
        let timeLimit: TimeInterval = 2 * 60 // 2分钟内可撤销
        return !isDeleted && 
               !isRevoked &&
               Date().timeIntervalSince(creationDate) < timeLimit
    }
    
    /// 撤销消息
    func revoke() {
        isRevoked = true
        revokeDate = Date()
    }
    
    /// 获取消息显示时间
    func getDisplayTime() -> String {
        let formatter = DateFormatter()
        let calendar = Calendar.current

        if calendar.isDate(creationDate, inSameDayAs: Date()) {
            formatter.dateFormat = "HH:mm"
        } else if calendar.isDate(creationDate, inSameDayAs: calendar.date(byAdding: .day, value: -1, to: Date()) ?? Date()) {
            formatter.dateFormat = "HH:mm"
            return "昨天 " + formatter.string(from: creationDate)
        } else {
            formatter.dateFormat = "MM/dd HH:mm"
        }

        return formatter.string(from: creationDate)
    }
    
    /// ✅ 新增：获取消息预览文本（用于通知）
    var preview: String {
        switch messageType {
        case .text:
            return content.count > 50 ? String(content.prefix(50)) + "..." : content
        case .image:
            return "[图片]"
        case .voice:
            return "[语音]"
        case .video:
            return "[视频]"
        case .habitShare:
            return "[习惯分享]"
        case .stellarEnergy:
            return "[星际能量]"
        case .aiSuggestion:
            return "[AI建议]"
        }
    }

    // MARK: - 🔑 新增：消息状态管理方法

    /// 🔑 标记消息为已发送状态
    func markAsSent() {
        self.status = .sent
    }

    /// 🔑 标记消息为发送失败状态
    func markAsFailed() {
        self.status = .failed
    }

    /// 🔑 标记消息为已送达状态
    func markAsDelivered() {
        self.status = .delivered
    }

    /// 🔑 标记消息为已读状态
    func markAsRead() {
        self.status = .read
        self.isRead = true
        self.readDate = Date()
    }

    /// 🔑 检查消息是否需要重发
    var needsResend: Bool {
        return status == .failed
    }

    /// 🔑 检查消息是否已成功发送
    var isSuccessfullySent: Bool {
        return status == .sent || status == .delivered || status == .read
    }
}
