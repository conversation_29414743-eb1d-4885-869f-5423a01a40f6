import SwiftUI
import SwiftData
import Photos

// MARK: - 精确滚动定位系统

// PreferenceKey定义已移至EAChatScrollingSystem.swift

/// 好友聊天界面 - AI增强聊天系统
/// 遵循iOS设计规范，支持实时聊天和AI智能建议
struct EAFriendChatView: View {
    
    // MARK: - 环境依赖
    
    @EnvironmentObject var sessionManager: EASessionManager
    @EnvironmentObject var chatService: EAFriendChatService  // ✅ 使用全局聊天服务实例
    @Environment(\.repositoryContainer) private var repositoryContainer
    @Environment(\.chatStateManager) private var chatStateManager
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - 多媒体服务依赖
    
    @StateObject private var permissionManager = EAPermissionManager()
    @State private var mediaService: EAMediaService?
    
    // MARK: - 状态管理

    let friendshipId: UUID  // 🔑 修复：使用ID而不是对象，避免跨Context问题
    // 🚨 架构修复：移除SwiftData模型对象持有，符合全局状态管理器设计规则
    // @State private var friendship: EAFriendship? = nil  // ❌ 违规：持有SwiftData模型对象
    @State private var messageText: String = ""
    @State private var showAISuggestions = false
    @State private var showAlert: Bool = false
    @State private var alertMessage: String = ""

    // ✅ 重构后的媒体查看器状态管理 - 使用item模式根治首次点击问题
    @State private var mediaViewerItem: EAMediaViewerItem? = nil
    @State private var showingImageActionSheet = false



    // 🔑 新增：初始化错误状态（用于优雅处理初始化失败）
    @State private var initializationError: Error? = nil



    // 🔑 性能优化：分阶段加载状态管理
    @State private var isInitialLoading = true  // 初始页面加载状态
    @State private var isServicesInitialized = false  // 服务初始化状态

    // 🔑 重构：将计算属性改为状态变量，避免同步访问currentUser
    @State private var currentUserProfile: EAUserSocialProfile? = nil
    @State private var currentUserAvatarData: EAAvatarData? = nil
    @State private var friendProfile: EAUserSocialProfile? = nil

    // 🚨 关键修复：强制UI刷新状态
    @State private var forceRefreshToggle: Bool = false

    // 🚨 数据完整性验证状态
    @State private var isValidatingDataIntegrity = false

    // 🔑 批次三新增：屏蔽状态管理
    @State private var isBlocked = false  // 当前用户是否被好友屏蔽
    @State private var hasBlockedFriend = false  // 当前用户是否屏蔽了好友
    @State private var showUnblockConfirmation = false  // 取消屏蔽确认对话框

    // MARK: - 智能滚动系统状态

    @State private var scrollState = ChatScrollState()
    @State private var needsPreciseScroll = false


    
    // MARK: - 初始化

    init(friendshipId: UUID) {  // 🔑 修复：只接收ID，避免跨Context对象传递
        self.friendshipId = friendshipId
        // 🔑 架构兼容性修复：完全移除init中的复杂初始化，使用占位服务
        // 真正的服务初始化在onAppear中进行，确保所有架构下都能成功编译
    }
    


    // MARK: - 数据获取辅助方法

    /// 🔑 异步获取friendship对象 - 符合架构规范，不持有SwiftData模型对象
    private func getFriendship() async -> EAFriendship? {
        guard let container = repositoryContainer else {
            await MainActor.run {
                showAlertMessage("系统初始化未完成，请稍后重试")
            }
            return nil
        }

        // 🔑 修复：获取当前用户ID用于数据所有权验证
        guard let currentUser = await sessionManager.safeCurrentUser else {
            await MainActor.run {
                showAlertMessage("无法获取当前用户信息")
            }
            return nil
        }

        do {
            let repository = container.friendshipRepository
            return try await repository.fetchFriendship(by: friendshipId, currentUserID: currentUser.id)
        } catch {
            await MainActor.run {
                showAlertMessage("获取好友关系失败：\(error.localizedDescription)")
            }
            return nil
        }
    }

    // MARK: - 精确滚动定位算法
    // 滚动逻辑已移至ChatScrollManager

    // MARK: - 主视图

    var body: some View {
        if let error = initializationError {
            // 🚨 显示初始化错误界面
            VStack(spacing: 20) {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 48))
                    .foregroundColor(.orange)
                
                Text("聊天功能初始化失败")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("部分功能可能受限，建议重启应用")
                    .font(.body)
                    .multilineTextAlignment(.center)
                    .foregroundColor(.secondary)
                
                Text("错误详情：\(error.localizedDescription)")
                    .font(.caption)
                    .foregroundColor(.red)
                    .padding(.horizontal)
                
                Button("继续使用") {
                    // 清除错误状态，允许用户继续使用
                    initializationError = nil
                }
                .buttonStyle(.borderedProminent)
                
                Button("返回") {
                    dismiss()
                }
                .buttonStyle(.bordered)
            }
            .padding()
            .background(EABackgroundView(style: .auraSpace, showParticles: true))
            .navigationBarTitleDisplayMode(.inline)
            .navigationTitle("聊天")
        } else {
            // 🎯 精确滚动定位系统：使用GeometryReader获取真实布局信息
            GeometryReader { geometry in
                ScrollViewReader { proxy in
                    ScrollView {
                        LazyVStack(spacing: 4) {
                            if !chatService.messages.isEmpty {
                                ForEach(Array(chatService.messages.enumerated()), id: \.element.id) { index, message in
                                    EAChatMessageBubble(
                                        message: message,
                                        isFromCurrentUser: isMessageFromCurrentUser(message),
                                        avatarData: isMessageFromCurrentUser(message) ? currentUserAvatarData : friendAvatarData,
                                        showTimestamp: shouldShowTimestamp(for: message, at: index),
                                        onImageTap: { path in
                                            handleImageTap(path: path, message: message)
                                        },
                                        getMediaURL: { path in
                                            return chatService.getMediaURL(for: message)
                                        }
                                    )
                                    .id(message.id)
                                }
                            } else {
                                // 空状态占位
                                EAEmptyChatStateView(
                                    friendName: friendDisplayName,
                                    onQuickStartTap: sendQuickStartMessage
                                )
                            }
                        }
                        .padding(.vertical, 8)
                        .padding(.horizontal, horizontalPadding)
                        // 🔑 监听消息容器高度变化
                        .background(
                            GeometryReader { messageGeometry in
                                Color.clear
                                    .preference(key: MessageContainerHeightPreferenceKey.self,
                                              value: messageGeometry.size.height)
                            }
                        )
                    }
                // 🔑 Apple官方标准方案：将输入工具栏嵌入到安全区域边缘
                .safeAreaInset(edge: .bottom) {
                    // AI建议栏和输入工具栏整体作为安全区域插入内容
                    VStack(spacing: 8) {
                        // AI建议栏
                        if !chatService.currentAISuggestions.isEmpty && !isInitialLoading {
                            EAAISuggestionBar(
                                suggestions: chatService.currentAISuggestions,
                                isVisible: true,
                                onSuggestionTap: { suggestion in
                                    Task {
                                        await chatService.sendAISuggestedMessage(suggestion)
                                    }
                                },
                                onDismiss: {
                                    chatService.currentAISuggestions.removeAll()
                                }
                            )
                            // 🔑 关键修复：限制AI建议栏高度
                            .frame(maxHeight: 80)
                        }

                        // 🔑 批次三修改：根据屏蔽状态显示不同的输入区域
                        if !isInitialLoading {
                            if isBlocked || hasBlockedFriend {
                                // 屏蔽状态提示栏
                                blockedStatusBar
                            } else {
                                // 正常输入工具栏
                                messageInputBar
                                    // 🔑 关键修复：移除外层高度限制，让EACustomTextInput自己控制高度
                                    // 🚨 根本问题：.frame(maxHeight: 140pt)强制设置了过高的高度限制
                                    // 现在让组件自然布局，遵循微信/iMessage的36pt单行标准
                                    .animation(.interactiveSpring(response: 0.2, dampingFraction: 0.8, blendDuration: 0.25), value: messageText)
                                    .background(
                                        GeometryReader { inputGeometry in
                                            Color.clear
                                                .preference(key: InputBarHeightPreferenceKey.self,
                                                          value: inputGeometry.size.height)
                                        }
                                    )
                            }
                        }
                    }
                    // 🔑 关键修复：移除过高的外部高度限制，让组件自然布局
                    // 🚨 架构重构：移除手动padding补偿，由父级EAMainTabView统一管理安全区域
                    // 现在使用标准的safeAreaInset机制，确保与自定义Tab栏的精确避让
                    .background(stellarUniverseBackground)
                }
                // 🔑 监听布局变化，实现智能滚动定位
                .onPreferenceChange(InputBarHeightPreferenceKey.self) { height in
                    scrollState.updateInputBarHeight(height)
                    if needsPreciseScroll && !chatService.messages.isEmpty,
                       let lastMessage = chatService.messages.last,
                       scrollState.shouldPerformScroll(for: lastMessage.id) {
                        scrollState.startScrolling()
                        ChatScrollManager.performSmartScroll(to: lastMessage.id, using: {
                            proxy.scrollTo(lastMessage.id, anchor: .bottom)
                        }, completion: {
                            scrollState.finishScrolling()
                        })
                        needsPreciseScroll = false
                    }
                }
                .onPreferenceChange(MessageContainerHeightPreferenceKey.self) { height in
                    scrollState.updateMessageContainerHeight(height)
                    if needsPreciseScroll && !chatService.messages.isEmpty,
                       let lastMessage = chatService.messages.last,
                       scrollState.shouldPerformScroll(for: lastMessage.id) {
                        scrollState.startScrolling()
                        ChatScrollManager.performSmartScroll(to: lastMessage.id, using: {
                            proxy.scrollTo(lastMessage.id, anchor: .bottom)
                        }, completion: {
                            scrollState.finishScrolling()
                        })
                        needsPreciseScroll = false
                    }
                }
                .onAppear {
                    // 🎯 页面出现时标记需要智能滚动
                    scrollState.updateAvailableHeight(geometry.size.height)
                    needsPreciseScroll = true

                    // 立即尝试滚动（如果消息已加载）
                    if !chatService.messages.isEmpty,
                       let lastMessage = chatService.messages.last,
                       scrollState.shouldPerformScroll(for: lastMessage.id) {
                        scrollState.startScrolling()
                        ChatScrollManager.performSmartScroll(to: lastMessage.id, using: {
                            proxy.scrollTo(lastMessage.id, anchor: .bottom)
                        }, completion: {
                            scrollState.finishScrolling()
                        })
                        needsPreciseScroll = false
                    }
                }
                .onChange(of: chatService.messages.count) { _, _ in
                    // 🎯 新消息到来时执行智能滚动
                    if !chatService.messages.isEmpty,
                       let lastMessage = chatService.messages.last,
                       scrollState.shouldPerformScroll(for: lastMessage.id) {
                        scrollState.startScrolling()
                        ChatScrollManager.performSmartScroll(to: lastMessage.id, using: {
                            proxy.scrollTo(lastMessage.id, anchor: .bottom)
                        }, completion: {
                            scrollState.finishScrolling()
                        })
                    }
                    forceRefreshToggle.toggle()
                }
                .onChange(of: isInitialLoading) { _, newValue in
                    // 🎯 初始加载完成时执行智能滚动
                    if !newValue && !chatService.messages.isEmpty,
                       let lastMessage = chatService.messages.last,
                       scrollState.shouldPerformScroll(for: lastMessage.id) {
                        scrollState.startScrolling()
                        ChatScrollManager.performSmartScroll(to: lastMessage.id, using: {
                            proxy.scrollTo(lastMessage.id, anchor: .bottom)
                        }, completion: {
                            scrollState.finishScrolling()
                        })
                    }
                }
                }
            }
            .background(stellarUniverseBackground)
            .navigationBarTitleDisplayMode(.inline)
            .navigationTitle(friendName)
            .toolbarBackground(.visible, for: .navigationBar)
            .toolbarBackground(Color.chatBackgroundDeep.opacity(0.95), for: .navigationBar)
            // 🚨 关键修复：完全移除错误的安全区域忽略指令
            .onAppear {
                // 🚨 关键修复：验证用户登录状态，避免空用户崩溃
                guard sessionManager.currentUserID != nil else {
                    showAlertMessage("用户未登录，请重新登录")
                    return
                }

                isInitialLoading = true

                // 🔑 修复执行顺序：优化异步操作链，添加状态恢复
                Task {
                    // 🚨 第一步：初始化服务
                    await initializeServicesIfNeeded()

                    // 🔑 第二步：恢复聊天状态
                    await restoreChatState()

                    // 🚨 第三步：加载friendship对象
                    await loadFriendshipAndStartChat()

                    // 🚨 第四步：验证数据完整性（此时friendship已加载）
                    let dataIntegrityValid = await validateChatDataIntegrity()

                    if !dataIntegrityValid {
                        await MainActor.run {
                            isInitialLoading = false
                        }
                        return
                    }

                    // 🚨 第四步：加载用户档案数据
                    await loadUserProfiles()

                    // 🔑 批次三修复：第五步：安全检查屏蔽状态
                    // 确保friendProfile已加载后再检查屏蔽状态
                    if friendProfile != nil {
                        await checkBlockingStatus()
                    } else {
                        #if DEBUG
                        print("⚠️ friendProfile未加载，跳过屏蔽状态检查")
                        #endif
                        // 设置默认状态
                        await MainActor.run {
                            hasBlockedFriend = false
                            isBlocked = false
                        }
                    }

                    await MainActor.run {
                        isInitialLoading = false
                    }
                }
            }
            .onDisappear {
                // 🔑 关键修复：页面消失时保存聊天状态，确保消息不丢失
                Task { @MainActor in
                    // 🔑 保存当前聊天状态到服务中，避免页面切换时数据丢失
                    await saveChatState()

                    // 简单的状态清理，不涉及键盘操作
                    isInitialLoading = false
                }
            }
            // 🔑 新增：监听头像更新通知，确保聊天页面头像与"我的"页面保持同步
            .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("EAUserAvatarDidUpdate"))) { notification in
                // 当头像更新时，触发UI重新渲染
                if let userId = notification.userInfo?["userId"] as? String,
                   userId == sessionManager.currentUserID?.uuidString {
                    // 🔑 修复：重新加载用户档案数据
                    Task {
                        await loadUserProfiles()
                    }
                }
            }
            // 🔑 重构：监听用户ID变化，使用新的安全模式
            .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("EAUserDataDidUpdate"))) { notification in
                if let userId = notification.userInfo?["userId"] as? String,
                   userId == sessionManager.currentUserID?.uuidString {
                    // 🔧 触发UI更新，重新加载用户档案数据
                    Task {
                        await loadUserProfiles()
                    }
                }
            }
            .alert("提示", isPresented: $showAlert) {
                Button("确定") { }
            } message: {
                Text(alertMessage)
            }
            // 🔑 批次三新增：取消屏蔽确认对话框
            .alert("确认取消屏蔽", isPresented: $showUnblockConfirmation) {
                Button("取消", role: .cancel) { }
                Button("确认取消") {
                    Task {
                        await handleUnblockFriend()
                    }
                }
            } message: {
                Text("取消屏蔽后，您和该用户将可以正常发送消息。确定要取消屏蔽吗？")
            }
            .fullScreenCover(item: $mediaViewerItem) { item in
                // 🔧 使用item模式，修复关闭按钮功能
                EAChatMediaViewer(
                    mediaURL: item.url,
                    mediaType: item.mediaType,
                    isPresented: Binding(
                        get: { mediaViewerItem != nil },
                        set: { newValue in
                            if !newValue {
                                mediaViewerItem = nil
                            }
                        }
                    )
                )
                .onDisappear {
                    // 清理状态，为下次使用做准备
                    mediaViewerItem = nil
                }
            }
            .confirmationDialog("图片操作", isPresented: $showingImageActionSheet, titleVisibility: .visible) {
                Button("保存到相册") {
                    if let mediaURL = mediaViewerItem?.url {
                        saveImageToPhotos(mediaURL)
                    }
                }
                Button("取消", role: .cancel) { }
            }
            // 🚨 关键修复：使用不可见的状态变化强制触发视图重绘
            .opacity(forceRefreshToggle ? 1.0 : 1.0)
        }
    }
    
    // MARK: - 子视图组件

    /// 星域数字宇宙背景 - 与好友列表页面保持一致的设计
    private var stellarUniverseBackground: some View {
        ZStack {
            // 深空背景 - 优化为更有层次感的宇宙色调
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.hexColor("0B1426"), // 深蓝夜空
                    Color.hexColor("1A2332"), // 中层蓝灰
                    Color.hexColor("2D3748"), // 浅层灰蓝
                    Color.hexColor("1A365D")  // 深青蓝
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()

            // 星云效果层
            RadialGradient(
                gradient: Gradient(colors: [
                    Color.hexColor("40E0D0").opacity(0.08),
                    Color.clear
                ]),
                center: .topTrailing,
                startRadius: 50,
                endRadius: 300
            )
            .ignoresSafeArea()

            // 星点效果 - 增加青色调星点
            ForEach(0..<40, id: \.self) { index in
                Circle()
                    .fill(
                        index % 3 == 0
                        ? Color.hexColor("40E0D0").opacity(Double.random(in: 0.3...0.7))
                        : Color.white.opacity(Double.random(in: 0.2...0.5))
                    )
                    .frame(width: CGFloat.random(in: 1...3))
                    .position(
                        x: CGFloat.random(in: 0...UIScreen.main.bounds.width),
                        y: CGFloat.random(in: 0...UIScreen.main.bounds.height)
                    )
            }
        }
    }


    // 🔄 重构：使用现有的EABackgroundView组件，减少代码重复
    
    // AI建议栏已移至EAAISuggestionBar.swift组件
    
    /// 消息输入栏 - 统一使用EAMediaInputToolbar
    @ViewBuilder
    private var messageInputBar: some View {
        // 🎯 架构简化：统一使用EAMediaInputToolbar，根据服务可用性控制媒体按钮显示
        if let mediaService = mediaService, isServicesInitialized {
            EAMediaInputToolbar(
                messageText: $messageText,
                mediaService: mediaService,
                permissionManager: permissionManager,
                showMediaButtons: true,
                onSendText: handleSendText,
                onSendVoice: handleSendVoice,
                onSendImages: handleSendImages,
                onSendVideo: handleSendVideo
            )
        } else {
            // 基础文本输入框（服务未初始化时的降级方案）
            basicTextInputBar
        }
    }

    /// 基础文本输入栏 - 服务未初始化时的降级方案
    @ViewBuilder
    private var basicTextInputBar: some View {
        HStack(spacing: 8) {
            EACustomTextInput(
                text: $messageText,
                placeholder: "输入消息...",
                maxLines: 6,
                fontSize: 16,
                textColor: .white,
                backgroundColor: .clear,
                placeholderColor: .lightGray
            )
            .background(EAChatInputBackground())

            // 发送按钮
            if !messageText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                EAChatSendButton(
                    isEnabled: true,
                    action: { handleSendText(messageText.trimmingCharacters(in: .whitespacesAndNewlines)) }
                )
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .animation(.interactiveSpring(response: 0.2, dampingFraction: 0.8, blendDuration: 0.25), value: messageText)
    }

    /// 🔑 批次三新增：屏蔽状态提示栏
    @ViewBuilder
    private var blockedStatusBar: some View {
        VStack(spacing: 12) {
            // 状态提示
            HStack(spacing: 8) {
                Image(systemName: "eye.slash.fill")
                    .font(.system(size: 16))
                    .foregroundColor(.red)

                Text(blockingStatusMessage)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))

                Spacer()
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.red.opacity(0.2))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.red.opacity(0.4), lineWidth: 1)
                    )
            )
            .padding(.horizontal, 16)

            // 取消屏蔽按钮（仅当前用户屏蔽了好友时显示）
            if hasBlockedFriend {
                Button(action: {
                    showUnblockConfirmation = true
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: "eye.fill")
                            .font(.system(size: 14))

                        Text("取消屏蔽")
                            .font(.system(size: 14, weight: .medium))
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 10)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(Color.blue.opacity(0.8))
                    )
                }
                .padding(.horizontal, 16)
            }
        }
        .padding(.vertical, 8)
        .background(stellarUniverseBackground)
    }

    /// 🔑 批次三新增：屏蔽状态消息
    private var blockingStatusMessage: String {
        if hasBlockedFriend {
            return "您已屏蔽该用户，无法发送消息"
        } else if isBlocked {
            return "该用户已屏蔽您，无法发送消息"
        } else {
            return "消息功能暂时不可用"
        }
    }

    /// 加载视图
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: Color.chatAccentPrimary))
                .scaleEffect(1.2)
            
            Text("加载聊天记录中...")
                .font(.system(size: 16))
                .foregroundColor(.white.opacity(0.7))
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    

    
    /// 发送快速开场白消息
    private func sendQuickStartMessage(_ text: String) {
        messageText = text
        Task {
            guard let friendship = await getFriendship() else { return }
            await chatService.sendMessage(content: text, messageType: .text, friendship: friendship)
            messageText = ""
        }
    }
    
    // MARK: - 异步数据加载方法

    /// 🔑 重构：异步加载用户档案数据（增强防御性编程）
    @MainActor
    private func loadUserProfiles() async {
        // 🚨 关键修复：加载当前用户数据，增加完整性验证
        guard let currentUser = await sessionManager.safeCurrentUser else {
            #if DEBUG
            print("🚨 loadUserProfiles失败: 无法获取当前用户")
            #endif
            showAlertMessage("用户会话异常，请重新登录")
            return
        }

        // 🔑 验证用户社交档案完整性
        guard let socialProfile = currentUser.socialProfile else {
            #if DEBUG
            print("🚨 loadUserProfiles失败: 当前用户缺少社交档案")
            #endif
            showAlertMessage("用户档案不完整，请重新登录")
            return
        }

        currentUserProfile = socialProfile
        currentUserAvatarData = currentUser.avatarData

        #if DEBUG
        print("✅ 当前用户档案加载成功: \(currentUser.username)")
        #endif

        // 🚨 关键修复：加载好友档案数据，增加错误处理
        guard let friendship = await getFriendship() else {
            #if DEBUG
            print("🚨 loadUserProfiles失败: 无法获取好友关系")
            #endif
            showAlertMessage("好友关系获取失败，请返回重试")
            return
        }

        guard let otherProfile = friendship.getOtherProfile(currentProfile: socialProfile) else {
            #if DEBUG
            print("🚨 loadUserProfiles失败: 无法获取好友档案")
            #endif
            showAlertMessage("好友档案获取失败，请返回重试")
            return
        }

        // 🔑 验证好友档案完整性
        guard let friendUser = otherProfile.user else {
            #if DEBUG
            print("🚨 loadUserProfiles失败: 好友用户数据不完整")
            #endif
            showAlertMessage("好友数据不完整，请稍后重试")
            return
        }

        friendProfile = otherProfile

        #if DEBUG
        print("✅ 好友档案加载成功: \(friendUser.username)")
        #endif
    }

    /// 🔑 修复：获取好友最新的头像数据
    private var friendAvatarData: EAAvatarData? {
        // 从好友档案中获取最新的头像数据，确保与设置的头像一致
        return friendProfile?.user?.avatarData
    }
    
    /// 好友显示名称
    private var friendDisplayName: String {
        return friendProfile?.user?.username ?? "未知用户"
    }

    /// 好友名称（用于导航栏标题）
    private var friendName: String {
        return friendProfile?.user?.username ?? "聊天"
    }
    
    /// 好友姓名首字母
    private var friendInitials: String {
        let name = friendDisplayName
        return String(name.prefix(2)).uppercased()
    }
    
    /// 是否在线
    private var isOnline: Bool {
        // 🔑 暂时返回false，等待在线状态功能实现
        return false
    }

    /// 水平边距 - 适配iPad和iPhone
    private var horizontalPadding: CGFloat {
        return UIDevice.current.userInterfaceIdiom == .pad ? 40 : 16
    }
    
    // MARK: - 私有方法

    /// 🔑 架构兼容性修复：安全初始化复杂服务
    private func initializeServicesIfNeeded() async {
        guard !isServicesInitialized else { return }
        
        do {
            // ✅ 修复：EAFileStorageService()是同步初始化，移除不必要的await
            let fileStorage = try EAFileStorageService()
            
            // 🔑 确保UI更新在主线程
            await MainActor.run {
            self.mediaService = EAMediaService(
                permissionManager: self.permissionManager,
                fileStorageService: fileStorage
            )
                self.isServicesInitialized = true
                self.initializationError = nil
            }

            // 媒体服务初始化成功

        } catch {
            // 媒体服务初始化失败，继续执行但功能受限
            
            await MainActor.run {
                self.initializationError = error
                self.isServicesInitialized = false
                // 🚨 关键修复：即使媒体服务初始化失败，也要确保基本聊天功能可用
                // mediaService保持nil，但不影响基本文本聊天
            }
        }
    }
    
    // MARK: - 媒体输入处理方法

    /// 处理文本消息发送
    private func handleSendText(_ text: String) {
        Task {
            guard let friendship = await getFriendship() else { return }
            await chatService.sendMessage(content: text, messageType: .text, friendship: friendship)
        }
    }

    /// 处理语音消息发送
    private func handleSendVoice(_ voiceURL: URL) {
        Task {
            guard let friendship = await getFriendship() else { return }
            await chatService.sendMessage(content: voiceURL.path, messageType: .voice, friendship: friendship)
        }
    }

    /// 处理图片消息发送
    private func handleSendImages(_ images: [UIImage]) {
        Task {
            guard let friendship = await getFriendship() else { return }
            // 🔧 修复：使用专用的图片发送服务，避免路径处理错误
            for image in images {
                guard let imageData = image.jpegData(compressionQuality: 0.8) else { continue }
                await chatService.sendImageMessage(imageData: imageData, friendship: friendship)
            }
        }
    }

    /// 处理视频消息发送
    private func handleSendVideo(_ videoURL: URL) {
        Task {
            guard let friendship = await getFriendship() else { return }
            // 🔧 修复：使用专用的视频发送服务，避免路径处理错误
            await chatService.sendVideoMessage(videoURL: videoURL, friendship: friendship)
        }
    }

    // MARK: - 数据加载方法

    /// 🔑 修复：先加载friendship，再开始聊天会话，优化异步操作链
    /// 🚨 阶段1.1增强：集成用户身份完整性检查，消除崩溃风险
    private func loadFriendshipAndStartChat() async {
        do {
            // 🚨 关键修复：验证SessionManager状态和用户登录状态
            guard sessionManager.isLoggedIn,
                  let currentUser = await sessionManager.safeCurrentUser else {
                await MainActor.run {
                    showAlertMessage("用户未登录或会话已过期，请重新登录")
                }
                return
            }
            
            // 根据ID从当前Context查询friendship
            guard let container = repositoryContainer else {
                await MainActor.run {
                    showAlertMessage("系统错误：Repository容器未初始化")
                }
                return
            }
            
            // 🔑 关键修复：确保用户有社交档案，如果没有则创建
            var currentUserProfile: EAUserSocialProfile
            if let existingProfile = currentUser.socialProfile {
                currentUserProfile = existingProfile
            } else {
                // 🚨 紧急修复：用户缺少社交档案，立即创建
                do {
                    currentUserProfile = try await container.userRepository.ensureSocialProfile(for: currentUser)
                    #if DEBUG
                    #if DEBUG
                    // 调试环境记录：为用户创建了缺失的社交档案
                    #endif
                    #endif
                } catch {
                    await MainActor.run {
                        showAlertMessage("无法创建用户社交档案，请重新登录")
                    }
                    return
                }
            }
            
            // 🔑 验证社交档案数据完整性
            if currentUserProfile.stellarLevel == nil {
                currentUserProfile.initializeDigitalUniverseData()
            }
            
            #if DEBUG
                            #if DEBUG
                // 调试环境记录：用户社交档案验证完成
                #endif
            #endif
            
            // 🔑 性能优化：并行处理用户数据刷新和friendship加载
            // 🚨 关键修复：使用正确的Repository方法名，并添加数据所有权验证
            async let friendshipTask = container.friendshipRepository.fetchFriendship(by: friendshipId, currentUserID: currentUser.id)
            
            // 🔑 关键修复：移除错误的sessionManager.updateUser调用
            // 原问题：调用了不存在的updateUser方法导致崩溃
            // 修复方案：不需要在此处刷新用户数据，SessionManager会自动处理用户状态
            // if let currentUserId = sessionManager.currentUser?.id {
            //     Task.detached(priority: .background) {
            //         if let refreshedUser = try? await container.userRepository.fetchUser(id: currentUserId) {
            //             try? await sessionManager.updateUser(refreshedUser)  // ❌ 方法不存在，导致崩溃
            //         }
            //     }
            // }
            
            // 等待friendship加载完成
            let loadedFriendship = try await friendshipTask

            if let loadedFriendship = loadedFriendship {
                // 🚨 关键修复：在主线程中进行所有UI相关的验证和操作，避免跨Context访问
                await MainActor.run {
                    // 🔑 阶段1.1优化：使用完整性守护验证好友关系
                    guard loadedFriendship.isValidFriendship() else {
                        showAlertMessage("好友关系数据异常，请返回重试")
                        return
                    }

                    // 🔑 阶段1.1优化：使用已验证的currentUserProfile，避免重复获取
                    // 验证当前用户是否为好友关系的参与者
                    
                    // 🚨 关键修复：安全地获取对方档案，避免Context冲突
                    // 先检查关系属性是否存在，再进行比较
                    guard loadedFriendship.initiatorProfile != nil && loadedFriendship.friendProfile != nil else {
                        showAlertMessage("好友关系数据不完整，请返回重试")
                        return
                    }
                    
                    // 🔑 新增：验证好友用户的社交档案完整性
                    guard let otherProfile = loadedFriendship.getOtherProfile(currentProfile: currentUserProfile) else {
                        showAlertMessage("无法验证好友关系，请返回重试")
                        return
                    }
                    
                    // 🔑 关键修复：验证好友的社交档案数据完整性
                    guard otherProfile.user != nil,
                          otherProfile.stellarLevel != nil,
                          otherProfile.totalStellarEnergy != nil,
                          otherProfile.explorerTitle != nil else {
                        showAlertMessage("好友数据不完整，可能需要好友重新登录")
                        return
                    }

                    // 好友用户验证完成，继续初始化聊天服务
                    // 🚨 架构修复：不再持有SwiftData模型对象，符合全局状态管理器设计规则
                }

                // 🔑 重要：开始聊天会话（这会加载历史消息）
                // 🚨 关键修复：确保chatService操作在正确的Context中执行
                await chatService.startChatSession(with: loadedFriendship)

            } else {
                // 无法找到好友关系
                await MainActor.run {
                    showAlertMessage("无法找到好友关系")
                }
            }
        } catch {
            // 加载好友关系失败
            await MainActor.run {
                // 🔑 增强错误处理：根据错误类型提供更友好的提示
                let errorMessage: String
                if error.localizedDescription.contains("Context") || error.localizedDescription.contains("context") {
                    errorMessage = "数据同步异常，请返回重试"
                } else if error.localizedDescription.contains("not found") {
                    errorMessage = "好友关系不存在，可能已被删除"
                } else {
                    errorMessage = "加载好友关系失败：\(error.localizedDescription)"
                }
                showAlertMessage(errorMessage)
            }
        }
    }
    
    /// 判断消息是否来自当前用户
    private func isMessageFromCurrentUser(_ message: EAFriendMessage) -> Bool {
        return message.senderProfile?.user?.id == sessionManager.currentUserID
    }
    
    /// 🔑 新增：判断是否需要显示时间戳
    private func shouldShowTimestamp(for message: EAFriendMessage, at index: Int) -> Bool {
        // 首条消息总是显示时间戳
        if index == 0 { return true }
        
        // 与上一条消息比较时间差
        let previousMessage = chatService.messages[index - 1]
        let timeDifference = message.creationDate.timeIntervalSince(previousMessage.creationDate)
        
        // 时间差超过5分钟或跨天则显示
        return timeDifference > 300 || !Calendar.current.isDate(message.creationDate, inSameDayAs: previousMessage.creationDate)
    }
    
    /// 🔑 重构：处理图片点击事件 - 使用item模式根治首次点击问题
    private func handleImageTap(path: String, message: EAFriendMessage) {
        guard let mediaURL = chatService.getMediaURL(for: message) else {
            showAlertMessage("媒体文件不存在或已损坏")
            return
        }

        let mediaType: EAChatMediaViewer.MediaType = EAChatMediaManager.isVideoFile(path) ? .video : .image

        // 🔧 使用item模式，根治首次点击问题
        mediaViewerItem = EAMediaViewerItem(url: mediaURL, mediaType: mediaType)
    }
    

    
    /// 删除消息
    private func deleteMessage(_ message: EAFriendMessage) {
        Task {
            await chatService.deleteMessage(message)
        }
    }
    
    /// 撤销消息
    private func revokeMessage(_ message: EAFriendMessage) {
        Task {
            await chatService.revokeMessage(message)
        }
    }
    
    /// 保存图片到相册 - 使用现代API
    private func saveImageToPhotos(_ mediaURL: URL) {
        guard let image = UIImage(contentsOfFile: mediaURL.path) else {
            showAlertMessage("无法加载图片")
            return
        }
        
        // 使用现代Photos框架替代已弃用的UIImageWriteToSavedPhotosAlbum
        Task {
            let hasPermission = await permissionManager.checkPhotoLibraryPermission()
            if hasPermission {
                // 保存图片到相册
                do {
                    try await PHPhotoLibrary.shared().performChanges {
                        PHAssetCreationRequest.creationRequestForAsset(from: image)
                    }
                    showAlertMessage("图片已保存到相册")
                } catch {
                    showAlertMessage("保存失败：\(error.localizedDescription)")
                }
            } else {
                showAlertMessage("需要相册权限才能保存图片")
            }
        }
    }
    
    /// 显示提示消息
    private func showAlertMessage(_ message: String) {
        alertMessage = message
        showAlert = true
    }

    /// 🔑 新增：重置键盘状态，解决RTIInputSystemClient冲突
    private func resetKeyboardState() {
        // 强制结束所有编辑状态
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)

        // 清理窗口编辑状态
        DispatchQueue.main.async {
            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
               let window = windowScene.windows.first {
                window.endEditing(true)
            }
        }
    }

    // MARK: - 屏蔽状态管理方法

    /// 🔑 批次三修复：检查屏蔽状态（增强防御性编程）
    private func checkBlockingStatus() async {
        // 🚨 关键修复：增加防御性检查，确保所有依赖都已就绪
        guard let repositoryContainer = repositoryContainer else {
            #if DEBUG
            print("🚨 屏蔽状态检查跳过: repositoryContainer未初始化")
            #endif
            return
        }

        guard let currentUser = await sessionManager.safeCurrentUser else {
            #if DEBUG
            print("🚨 屏蔽状态检查跳过: 无法获取当前用户")
            #endif
            return
        }

        // 🔑 关键修复：确保friendProfile已加载
        guard let friendProfile = friendProfile,
              let friendUserId = friendProfile.user?.id else {
            #if DEBUG
            print("🚨 屏蔽状态检查跳过: friendProfile未加载或用户ID无效")
            #endif
            // 🔑 设置默认状态，避免UI显示异常
            await MainActor.run {
                hasBlockedFriend = false
                isBlocked = false
            }
            return
        }

        do {
            let blockingService = repositoryContainer.blockingService

            // 🔑 修复：添加超时保护，避免无限等待
            let blockingCheckTask = Task {
                // 并行检查双向屏蔽状态
                async let currentUserBlockedFriendTask = blockingService.isUserBlockedAsync(
                    currentUserID: currentUser.id,
                    userID: friendUserId
                )

                async let friendBlockedCurrentUserTask = blockingService.isUserBlockedAsync(
                    currentUserID: friendUserId,
                    userID: currentUser.id
                )

                return await (currentUserBlockedFriendTask, friendBlockedCurrentUserTask)
            }

            // 🔑 添加5秒超时保护
            let result = try await withTimeout(seconds: 5) {
                await blockingCheckTask.value
            }

            await MainActor.run {
                hasBlockedFriend = result.0
                isBlocked = result.1
                #if DEBUG
                print("✅ 屏蔽状态检查完成: hasBlockedFriend=\(result.0), isBlocked=\(result.1)")
                #endif
            }

        } catch {
            // 🔑 修复：提供更详细的错误处理和回退机制
            await MainActor.run {
                // 设置安全的默认状态
                hasBlockedFriend = false
                isBlocked = false
            }

            #if DEBUG
            print("🚨 屏蔽状态检查失败: \(error.localizedDescription)")
            #endif
        }
    }

    /// 🔑 新增：超时保护辅助方法
    private func withTimeout<T>(seconds: TimeInterval, operation: @escaping () async throws -> T) async throws -> T {
        return try await withThrowingTaskGroup(of: T.self) { group in
            group.addTask {
                try await operation()
            }

            group.addTask {
                try await Task.sleep(nanoseconds: UInt64(seconds * 1_000_000_000))
                throw TimeoutError()
            }

            guard let result = try await group.next() else {
                throw TimeoutError()
            }

            group.cancelAll()
            return result
        }
    }

    /// 🔑 新增：超时错误类型
    private struct TimeoutError: Error {
        let localizedDescription = "操作超时"
    }

    /// 🔑 批次三新增：处理取消屏蔽操作
    private func handleUnblockFriend() async {
        do {
            guard let repositoryContainer = repositoryContainer,
                  let currentUser = await sessionManager.safeCurrentUser,
                  let friendProfile = friendProfile,
                  let friendUserId = friendProfile.user?.id else {
                await MainActor.run {
                    showAlertMessage("操作失败：用户信息不完整")
                }
                return
            }

            let blockingService = repositoryContainer.blockingService

            // 执行取消屏蔽
            await blockingService.unblockUser(currentUserID: currentUser.id, userID: friendUserId)

            await MainActor.run {
                hasBlockedFriend = false
                showAlertMessage("已取消屏蔽")
                
                // 🔑 关键修复：发送状态变化通知，确保好友列表同步更新
                NotificationCenter.default.post(
                    name: .blockingStatusChanged,
                    object: nil,
                    userInfo: ["userId": friendUserId, "action": "unblocked"]
                )
            }
        } catch {
            await MainActor.run {
                showAlertMessage("取消屏蔽失败：\(error.localizedDescription)")
            }
        }
    }

    // MARK: - 工具方法
    // 🗑️ 已删除getRelativePath方法 - 路径处理现在由EAChatMediaManager统一管理

    // MARK: - 🔑 新增：聊天状态管理

    /// 🔑 关键新增：保存聊天状态，防止页面切换时数据丢失
    private func saveChatState() async {
        let unsentMessage = messageText.trimmingCharacters(in: .whitespacesAndNewlines)
        let messageToSave = unsentMessage.isEmpty ? nil : unsentMessage

        // 🔑 通知聊天服务保存当前状态
        await chatService.saveChatState(for: friendshipId, unsentMessage: messageToSave)

        // 🔑 聊天状态保存完成（生产环境静默执行）
    }

    /// 🔑 关键新增：恢复聊天状态，确保页面重新进入时数据完整
    private func restoreChatState() async {
        // 🔑 通知聊天服务恢复状态并获取未发送的消息
        let unsentMessage = await chatService.restoreChatState(for: friendshipId)

        // 🔑 恢复未发送的消息文本
        if let savedMessage = unsentMessage, !savedMessage.isEmpty {
            await MainActor.run {
                messageText = savedMessage
            }
        }

        // 🔑 聊天状态恢复完成（生产环境静默执行）
    }

    // MARK: - 数据完整性验证

    /// 🚨 关键新增：验证好友聊天数据完整性
    /// 确保双方用户数据完整，可以安全进行聊天
    private func validateChatDataIntegrity() async -> Bool {
        await MainActor.run {
            isValidatingDataIntegrity = true
        }

        defer {
            Task { @MainActor in
                isValidatingDataIntegrity = false
            }
        }

        // 验证当前用户完整性
        guard let currentUser = await sessionManager.safeCurrentUser else {
            await MainActor.run {
                self.showAlertMessage("用户会话异常，请重新登录")
            }
            return false
        }

        // 🔑 验证Repository容器可用性
        guard let repositoryContainer = repositoryContainer else {
            await MainActor.run {
                self.showAlertMessage("系统服务异常，请重启应用")
            }
            return false
        }

        // 🔑 获取好友关系
        guard let friendship = await getFriendship() else {
            await MainActor.run {
                self.showAlertMessage("好友信息不完整，请稍后重试")
            }
            return false
        }

        // 🔑 使用用户身份完整性守护验证
        let integrityGuard = EAUserIntegrityGuard(repositoryContainer: repositoryContainer)

        // 🔑 使用整合后的聊天数据完整性验证方法
        let isValid = await integrityGuard.validateChatDataIntegrity(currentUser: currentUser, friendship: friendship)

        if !isValid {
            await MainActor.run {
                self.showAlertMessage("聊天数据验证失败，请稍后重试")
            }
        }

        return isValid
    }
}



// MARK: - SwiftUI预览

/// SwiftUI预览 - 好友聊天界面
/// 提供开发时的可视化支持，符合iOS开发规范
#Preview("好友聊天界面") {
    // 🔑 创建预览所需的依赖注入
    let container = try! EAAppSchema.createPreviewContainer()
    let repositoryContainer = EARepositoryContainerImpl(modelContainer: container)
    let sessionManager = EASessionManager()
    let aiDataBridge = EACommunityAIDataBridge(repositoryContainer: repositoryContainer)
    let chatService = EAFriendChatService(
        repositoryContainer: repositoryContainer,
        sessionManager: sessionManager,
        aiDataBridge: aiDataBridge
    )

    // 🔑 创建示例好友关系用于预览
    let sampleFriendship = EAFriendship()

    return NavigationView {
        EAFriendChatView(friendshipId: sampleFriendship.id)
            .environmentObject(sessionManager)
            .environmentObject(chatService)
            .environment(\.repositoryContainer, repositoryContainer)
    }
    .preferredColorScheme(.dark) // 🎨 使用深色主题匹配星域风格
}

// MARK: - 聊天滚动通知扩展
extension Notification.Name {
    static let eaScrollToChatBottom = Notification.Name("eaScrollToChatBottom")
}

